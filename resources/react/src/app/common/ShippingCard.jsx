import React, { memo, useCallback, useContext, useEffect, useState } from "react";
import { Form } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Print from "../../assets/images/svg/print";
import { FormInput } from "../../components/ui/Input";
import ReactSelect from "../../components/ui/ReactSelect";
import { CALCULATION_ON_TYPE, LedgerType, toastType } from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAddLessCharges,
    customToFixed,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import {
    fetchLedgerById,
    fetchLedgerGroupList,
    getLedgerModelDetail,
    getTcsTdsGroupList,
} from "../../store/ledger/ledgerSlice";
import { fetchTdsTcsRate } from "../../store/rate/rateSlice";
import AddLedgerModal from "../modal/Ledger/LedgerModal";
import DocumentModal from "../common/DocumentModal";
import { errorToast } from "../../store/actions/toastAction";
import DynamicNoteDescriptionModal from "./DynamicNoteDescriptionModal";

const ShippingCard = memo(
    ({
        grandTotal,
        taxableValue,
        mainGrandTotal,
        setFinalAmount,
        finalAmount,
        isPurchase,
        showCreditLimit,
        isOcr,
        userPermission,
        setItems
    }) => {
        const { itemLegder, configuration, company, recurringMaster } = useSelector(state => state);
        const isNarationUpdate = recurringMaster?.getRecurringById?.data?.is_naration_update ?? true;
        const saleConfiguration = configuration?.configuration;
        const {
            isShipLedgerModel,
            openShipLedgerModel,
            closeShipLedgerModel,
            gstCalculation,
            setGstCalculation,
            addLessChanges,
            setAddLessChanges,
            tcsRate,
            setTcsRate,
            additionalCharges,
            setAdditionalCharges,
            gstValue,
            additionalGst,
            cessValue,
            setCessValue,
            isEditCalculation,
            setIsEditCalculation,
            setAdditionalGst,
            setTaxableValue,
            isIGSTCalculation,
            isSGSTCalculation,
            setCheckGroupLedgerType,
            gstQuote,
            setIsTcsAmountChange,
            isTcsAmountChange,
            isChangedTcs,
            setIsChangedTcs,
            classification,
            otherDetail,
            invoiceValue,
            setInvoiceValue,
            setIsCheckGstType,
            setisFieldsChanges,
            recurringInvoiceDetails,
            itemType,
            // Focus tracking for modal operations
            trackNextFocusElement,
            focusTrackedElement,
            clearTrackedElement
        } = useContext(StateContext);

        const dispatch = useDispatch();
        const [mainTotal, setMainTotal] = useState(mainGrandTotal);
        const [currentAction, setCurrentAction] = useState("");
        const {
            ledgerDetailOptions,
            ledgerEntityType,
            ledgerGstOption,
            ledgerGroupType,
            additionalChargesLedgersOption,
            additionalChargesDefaultLedgersOption,
            gstOptions,
            tcsOptions,
            addLessLedgersOption,
            addLessLedgersDefaultOption,
            discountOption,
            bankOptions
        } = useDropdownOption();
        const [noteCount, setNoteCount] = useState(0);
        const [isFocused, setIsFocused] = useState(false);
        const [noteTermsCount, setNoteTermsCount] = useState(0);
        const [isTermsFocused, setIsTermsFocused] = useState(false);
        const [changeLedgerIndex, setChangeLedgerIndex] = useState({
            name: "",
            index: 0,
        });
        const [ledgerModelName, setLedgerModelName] = useState({
            name: "",
            id: null,
        });
        const [showPlusIcon, setShowPlusIcon] = useState({
            charge: false,
            tax: false,
        });
        const [isRecurringDynamicNote, setIsRecurringDynamicNote] = useState(false);
        const isRecurring = window.location.pathname.includes("recurring-invoices");
        const [bankLedgerModelName, setBankLedgerModelName] = useState("");
        const [openLedgerModel, setOpenLedgerModel] = useState(false);

        const handleFocus = () => setIsFocused(true);
        const handleBlur = () => setIsFocused(false);

        const handleTermsFocus = () => setIsTermsFocused(true);
        const handleTermsBlur = () => setIsTermsFocused(false);

        useEffect(() => {
            if (isEditCalculation) {
                const gstTotal = isIGSTCalculation
                    ? parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
                    : parseFloat(gstValue?.sgstValue || 0) +
                    parseFloat(additionalGst || 0) +
                    parseFloat(gstValue?.cgstValue) +
                    parseFloat(additionalGst || 0);
                let total =
                    parseFloat(company?.company?.is_gst_applicable ? customToFixed(gstTotal, 2) : 0) +
                    parseFloat(taxableValue) +
                    parseFloat(company?.company?.is_gst_applicable ? cessValue : 0) +
                    parseFloat(saleConfiguration?.footer?.is_enabled_tcs_details ? tcsRate?.tcs_amount || 0 : 0);
                total = customToFixed(total, 2)
                setMainTotal(parseFloat(total));
            }
        }, [
            taxableValue,
            cessValue,
            tcsRate?.tcs_amount,
            isIGSTCalculation,
            gstValue,
            additionalGst,
        ]);

        useEffect(() => {
            if (isEditCalculation) {
                const addLessCharges = calculateAddLessCharges(addLessChanges, mainTotal);
                setAddLessChanges(addLessCharges);
            }
        }, [mainTotal]);

        useEffect(() => {
            setShowPlusIcon({
                ...showPlusIcon,
                charge: additionalCharges?.additional_detail?.length < 3 ? false : true,
            });
        }, [additionalCharges?.additional_detail]);

        const handleChargeChange = useCallback(
            (index, field, value, data) => {
                setisFieldsChanges(true);
                setIsCheckGstType(true);
                setIsEditCalculation(true);
                const newCharges = { ...additionalCharges };
                if (field === "ac_type") {
                    newCharges.additional_detail[index].ac_type = value;
                    if (value === 2) {
                        const numericValue = parseFloat(newCharges.additional_detail[index].ac_value);
                        if (numericValue > 100) {
                            newCharges.additional_detail[index].ac_value = 0;
                        } else if (numericValue < -100) {
                            newCharges.additional_detail[index].ac_value = 0;
                        }
                    }
                } else if (field === "ac_gst_rate_id") {
                    if (value.label === "Select GST") {
                        newCharges.additional_detail[index].ac_gst_rate_id = 0;
                    } else {
                        newCharges.additional_detail[index].ac_gst_rate_id = value;
                    }
                } else if (field === "ac_value") {
                    if (value === "" || /^-?\d*(\.\d{0,3})?$/.test(value)) {
                        const numericValue = parseFloat(value);
                        if (newCharges.additional_detail[index].ac_type === 2) {
                            if (numericValue > 100) {
                                newCharges.additional_detail[index].ac_value = 0;
                            } else if (numericValue < -100) {
                                newCharges.additional_detail[index].ac_value = 0;
                            } else {
                                newCharges.additional_detail[index].ac_value = value;
                            }
                        } else {
                            newCharges.additional_detail[index].ac_value = value;
                        }
                    }
                } else if (
                    field !== "ac_type" &&
                    field !== "ac_gst_rate_id" &&
                    field !== "ac_value"
                ) {
                    newCharges.additional_detail[index][field] = value;
                    newCharges.additional_detail[index].ac_gst_rate_id = {
                        label: data.gst_rate,
                        value: data.gst,
                        rate: data.gst_rate,
                    };
                }

                const { updatedCharges, acTotal, taxableValue } = calculateAdditionalCharges(
                    newCharges,
                    grandTotal,
                    isIGSTCalculation
                );

                setAdditionalCharges({
                    ...newCharges,
                    additional_detail: updatedCharges,
                });
                setAdditionalGst(acTotal);
                setTaxableValue(taxableValue);
            },
            [grandTotal, additionalCharges]
        );

        const handleAddCharge = () => {
            const newCharges = {
                ac_ledger_id: null,
                ac_type: 1,
                ac_value: "",
                ac_gst_rate_id: {
                    label: "",
                    value: 0,
                    rate: 0,
                },
                ac_total: 0.0,
            };
            if (additionalCharges?.additional_detail?.length < 3) {
                setAdditionalCharges({
                    ...additionalCharges,
                    additional_detail: [...additionalCharges.additional_detail, newCharges],
                });
                setShowPlusIcon({ ...showPlusIcon, charge: false });
                if (additionalCharges?.additional_detail?.length === 2) {
                    setShowPlusIcon({ ...showPlusIcon, charge: true });
                }
            } else {
                setShowPlusIcon({ ...showPlusIcon, charge: true });
            }
        };

        const handleAddTax = () => {
            const newAddLess = {
                al_ledger_id: "",
                al_is_show_in_print: 1,
                al_type: 1,
                al_value: 0,
                al_total: 0,
            };
            if (addLessChanges?.length < 3) {
                setAddLessChanges([...addLessChanges, newAddLess]);
                setShowPlusIcon({ ...showPlusIcon, tax: false });
                if (addLessChanges?.length === 2) {
                    setShowPlusIcon({ ...showPlusIcon, tax: true });
                }
            } else {
                setShowPlusIcon({ ...showPlusIcon, tax: true });
            }
        };

        const handleRemoveCharge = index => {
            setIsEditCalculation(true);
            const filteredCharges = additionalCharges?.additional_detail?.filter(
                (_, i) => i !== index
            );

            const newCharges = {
                ...additionalCharges,
                additional_detail: filteredCharges,
            };

            const { updatedCharges, acTotal, taxableValue } = calculateAdditionalCharges(
                newCharges,
                grandTotal,
                isIGSTCalculation
            );

            setAdditionalCharges({
                ...newCharges,
                additional_detail: updatedCharges,
            });
            setAdditionalGst(acTotal);
            setTaxableValue(taxableValue);
            setShowPlusIcon({ ...showPlusIcon, charge: false });
        };

        const handleRemoveTax = index => {
            setIsEditCalculation(true);
            const newCharges = addLessChanges?.filter((_, i) => i !== index);
            setAddLessChanges(newCharges);
            setShowPlusIcon({ ...showPlusIcon, tax: false });
        };

        const handleOpenAddLedger = (id, type, index) => {
            // Track the next focusable element before opening the modal
            const currentElement = document.activeElement;
            if (currentElement) {
                trackNextFocusElement(currentElement);
            }

            openShipLedgerModel();
            dispatch(fetchLedgerGroupList());
            setIsChangedTcs(true);
            if (id) {
                dispatch(fetchLedgerById(id));
                setLedgerModelName({ name: "Update Ledger", id: id });
            } else {
                setLedgerModelName({ name: "Add Ledger", id: null });
            }
            if (type === "addless") {
                setChangeLedgerIndex({ name: "addless", index: index });
                dispatch(getLedgerModelDetail(LedgerType.ADD_LESS_LEDGER));
                setCheckGroupLedgerType("addless");
                setCurrentAction(isPurchase ? "Expense" : "Income");
            } else if (type === "tcs") {
                dispatch(getTcsTdsGroupList(isPurchase ? 2 : 1, true));
                setCheckGroupLedgerType("tcs");
                setCurrentAction(isPurchase ? "TCS Receivable" : "Taxes - TCS");
                setChangeLedgerIndex({ name: "tcs", index: index });
            } else {
                setCurrentAction(isPurchase ? "Expense" : "Income");
                setChangeLedgerIndex({ name: "additional", index: index });
                dispatch(getLedgerModelDetail(LedgerType.ADDITIONAL_LEDGER));
                setCheckGroupLedgerType("additional");
            }
        };

        const handleTcsChange = (name, value) => {
            setisFieldsChanges(true);
            setIsEditCalculation(true);
            setIsChangedTcs(true);

            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0)  + parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0)  + parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0)  + parseFloat(additionalGst || 0);

            setInvoiceValue(newInvoiceValue);

            if (name === "tcs_tax_id" && !value) {
                setTcsRate(prevRate => ({
                    ...prevRate,
                    tcs_rate: "",
                    tcs_amount: "",
                }));
            }
            if (name === "tcs_tax_id" && value) {
                dispatch(
                    fetchTdsTcsRate(
                        value,
                        gstQuote?.party_ledger_id ?? null,
                        setTcsRate,
                        taxableValue,
                        newInvoiceValue
                    )
                );
            }
            if (name === "tcs_rate") {
                if (value > 100) {
                    return; // Prevent invalid rate
                }
                setTcsRate(prevRate => ({
                    ...prevRate,
                    tcs_rate: value,
                    tcs_amount:
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(newInvoiceValue * ((value || 0) / 100)).toFixed(2)
                            : parseFloat(taxableValue * ((value || 0) / 100)).toFixed(2),
                }));
            } else {
                setIsTcsAmountChange(true);
                setTcsRate(prevRate => ({
                    ...prevRate,
                    [name]: value,
                }));
            }
        };

        const handleCessChange = value => {
            setIsEditCalculation(true);
            setCessValue(value);

            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(value || 0) +
                  parseFloat(gstValue?.igstValue || 0)  + parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(value || 0) +
                  parseFloat(gstValue?.sgstValue || 0)  + parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0)  + parseFloat(additionalGst || 0);

            setInvoiceValue(newInvoiceValue);

            // Update the TCS rate
            setTcsRate(prevRate => {
                const calculatedTcsAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((prevRate.tcs_rate || 0) / 100)).toFixed(2)
                        : parseFloat(taxableValue * ((prevRate.tcs_rate || 0) / 100)).toFixed(2);

                return {
                    ...prevRate,
                    tcs_amount: calculatedTcsAmount == 0 ? "" : calculatedTcsAmount,
                };
            });
            if (value == 0) {
                setItems(prevItems => {
                    return prevItems.map(item => {
                        return {
                            ...item,
                            cessValue: 0,
                            cessRate: 0,
                        };
                    });
                });
            }
        };

        const handleAddLessChange = (index, name, e) => {
            setisFieldsChanges(true);
            setIsCheckGstType(true);
            setIsEditCalculation(true);
            const addLess = [...addLessChanges];
            let currentItem = addLess[index];
            currentItem[name] = e;
            if (name === "al_value") {
                let input = e;
                if (input === "") {
                    currentItem.al_value = "";
                    currentItem.al_total = null;
                } else if (/^-?\d*(\.\d*)?$/.test(input)) {
                    if (input.includes(".")) {
                        const [intPart, decPart] = input.split(".");
                        input = intPart + "." + decPart.slice(0, 3);
                    }

                    const numericValue = parseFloat(input);

                    if (currentItem.al_type === 2) {
                        if (numericValue > 100) {
                            currentItem.al_value = 100;
                        } else if (numericValue < -100) {
                            currentItem.al_value = null;
                        } else {
                            currentItem.al_value = input;
                        }
                        const baseAmount = (mainTotal * numericValue) / 100;
                        currentItem.al_total = baseAmount?.toFixed(2);
                    } else {
                        currentItem.al_value = input;
                        currentItem.al_total = input;
                    }
                }
            }
            if (name === "al_value" && currentItem.al_type == 2 && e > 100) {
                currentItem.al_value = "";
            }
            if (name === "al_is_show_in_print") {
                currentItem.al_is_show_in_print = e === true ? 1 : 0;
            }

            if (name === "al_type") {
                const acValue = parseFloat(currentItem.al_value);
                if (e == 2) {
                    if (acValue > 100) {
                        currentItem.al_value = 100;
                    } else if (acValue < -100) {
                        currentItem.al_value = null;
                    }
                    const baseAmount = (mainTotal * currentItem.al_value) / 100;
                    currentItem.al_total = baseAmount?.toFixed(2);
                } else {
                    currentItem.al_total = currentItem.al_value;
                }
            }
            const addLessCharges = calculateAddLessCharges(addLess, mainTotal);
            setAddLessChanges(addLessCharges);
        };

        const handleRoundOffChange = value => {
            setIsEditCalculation(true);
            if (value === "" || value === "-" || /^-?\d*\.?\d*$/.test(value)) {
                const roundOffValue = value;
                setGstCalculation(prev => ({
                    ...prev,
                    round_of_amount: roundOffValue,
                    is_round_off_not_changed: 0,
                }));

                const adjustedTotal = mainGrandTotal + parseFloat(roundOffValue || 0);
                setFinalAmount(adjustedTotal);
            }
        };

        const showCurrentData = id => {
            if (changeLedgerIndex.name !== "additional") {
                const updatedAddLessChanges = [...addLessChanges];
                updatedAddLessChanges[changeLedgerIndex.index] = {
                    ...updatedAddLessChanges[changeLedgerIndex.index],
                    al_ledger_id: id,
                };
                setAddLessChanges(updatedAddLessChanges);
            } else {
                const updatedCharges = { ...additionalCharges };

                updatedCharges.additional_detail[changeLedgerIndex.index] = {
                    ...updatedCharges.additional_detail[changeLedgerIndex.index],
                    ac_ledger_id: id,
                };
                setAdditionalCharges(updatedCharges);
            }
        };

        useEffect(() => {
            setNoteCount(additionalCharges?.note?.length || 0);
        }, [additionalCharges?.note]);

        const handleChangeNote = e => {
            const { value } = e.target;
            setNoteCount(value?.length);
            setAdditionalCharges({
                ...additionalCharges,
                note: e.target.value,
            });
        };

        useEffect(() => {
            if(additionalCharges?.terms_and_conditions !== null && additionalCharges?.terms_and_conditions !== undefined) {
                setNoteTermsCount(additionalCharges?.terms_and_conditions?.length || 0);
            }
        }, [additionalCharges?.terms_and_conditions]);

        const handleChangeTerms = e => {
            const { value } = e.target;
            setNoteTermsCount(value?.length);
            setAdditionalCharges({
                ...additionalCharges,
                terms_and_conditions: value,
            });
        };

        const handleBankChange = (e) => {
            setAdditionalCharges({
                ...additionalCharges,
                bank_id: e.value,
            });
        };

        const openPaymentLedgerModel = (id) => {
            // Track the next focusable element before opening the modal
            const currentElement = document.activeElement;
            if (currentElement) {
                trackNextFocusElement(currentElement);
            }

            if (id) {
                dispatch(fetchLedgerById(id));
                setBankLedgerModelName({ name: "Update Ledger", id: id });
            } else {
                setBankLedgerModelName({ name: "Add Ledger", id: "" });
            }
            dispatch(fetchLedgerGroupList());
            dispatch(getLedgerModelDetail(LedgerType.BANK_LEDGER));
            setCheckGroupLedgerType("payment");
            setCurrentAction("Bank");
            setOpenLedgerModel(true);
        };
        
        const closeBankLedgerModel = () =>{
            setOpenLedgerModel(false);
            // Focus on the tracked element after modal closes
            focusTrackedElement();
        }

        const uploadDocument = e => {
            const maxFiles = 15;
            const maxFileSize = 2 * 1024 * 1024;
            const fileInput = e.currentTarget;
            const validFiles = Array.from(fileInput?.files || []).filter(
                file => file?.size <= maxFileSize
            );

            // Ensure upload_document is an array
            const currentDocuments = additionalCharges?.upload_document || [];
            const currentFileCount = Array.isArray(currentDocuments)
                ? currentDocuments.filter(file => !file.is_uploaded_new).length
                : 0;
            const newFileCount = validFiles.length;
            const totalFileCount = currentFileCount + newFileCount;

            if (totalFileCount > maxFiles) {
                dispatch(
                    errorToast({
                        text: "Please Enter Max 15 Documents.",
                        type: toastType.ERROR,
                    })
                );
                e.currentTarget.value = "";
                return;
            } else if (validFiles.length < (fileInput?.files || []).length) {
                dispatch(
                    errorToast({
                        text: "Please Select a file less than 2MB",
                        type: toastType.ERROR,
                    })
                );
                e.target.value = "";
                return;
            } else {
                setAdditionalCharges(prevCharges => ({
                    ...prevCharges,
                    upload_document: [
                        ...(Array.isArray(prevCharges.upload_document)
                            ? prevCharges.upload_document.filter(file => !file.is_uploaded_new)
                            : []),
                        ...validFiles.map((file, index) => ({
                            original_url: "",
                            file: file,
                            id: currentFileCount + index + 1,
                            is_uploaded_new: true
                        })),
                    ],
                }));
            }
        };

        useEffect(() => {
            if (tcsRate?.tcs_tax_id && isChangedTcs && !isTcsAmountChange) {
                // Calculate the new invoice value
                const newInvoiceValue = isIGSTCalculation
                    ? parseFloat(taxableValue || 0) +
                      parseFloat(cessValue || 0) +
                      parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
                    : parseFloat(taxableValue || 0) +
                      parseFloat(cessValue || 0) +
                      parseFloat(gstValue?.sgstValue || 0) + parseFloat(additionalGst || 0) +
                      parseFloat(gstValue?.cgstValue || 0) + parseFloat(additionalGst || 0);

                // Update the invoice value
                setInvoiceValue(newInvoiceValue);

                // Update the TCS rate with the correct tcs_amount
                setTcsRate({
                    ...tcsRate,
                    tcs_amount:
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(
                                  newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)
                              ).toFixed(2)
                            : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(
                                  2
                              ),
                });
            }
        }, [
            taxableValue,
            cessValue,
            tcsRate?.tcs_rate,
            tcsRate?.tcs_tax_id,
            gstValue,
            isChangedTcs,
            isTcsAmountChange,
        ]);

        return (
            <>
                <div
                    className={isOcr ? " " : "d-flex flex-xl-row flex-column w-100 justify-content-between"}
                    style={{ gap: "30px" }}
                >
                    <div className="additional-info">
                        {saleConfiguration?.footer?.is_enable_narration ? (
                            <div className="mb-8">
                                <Form.Group className="position-relative form-floating-group">
                                    <textarea
                                        className="form-control floating-label-input"
                                        placeholder=""
                                        value={additionalCharges?.note}
                                        onChange={handleChangeNote}
                                        maxLength={5000}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                        disabled={!isNarationUpdate}
                                    ></textarea>
                                    {isFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {5000 - noteCount || 0}
                                        </p>
                                    )}
                                    <Form.Label htmlFor="note">Note</Form.Label>
                                </Form.Group>
                                {isRecurring && recurringInvoiceDetails?.frequency_details?.repeat_frequency !== "Custom" && <div className="mt-4 "> <span className="m-auto">Want to specify Dates Automatically?</span>  <button
                                    type="button"
                                    className="btn recurring_note_btn ml-2"
                                    onClick={() => setIsRecurringDynamicNote(true)}
                                    disabled={!isNarationUpdate }
                                >
                                    Yes
                                </button></div>}
                            </div>
                        ) : null}
                        {saleConfiguration?.footer?.is_enabled_terms_and_conditions ? (
                            <div className="mb-8">
                                <Form.Group className="position-relative form-floating-group">
                                    <textarea
                                        className="form-control floating-label-input"
                                        placeholder=""
                                        value={additionalCharges?.terms_and_conditions?.slice(0, 5000)}
                                        maxLength={5000}
                                        onChange={handleChangeTerms}
                                        onFocus={handleTermsFocus}
                                        onBlur={handleTermsBlur}
                                    ></textarea>
                                    {isTermsFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {Math.max(5000 - (noteTermsCount ?? 0), 0)}
                                        </p>
                                    )}
                                    <Form.Label htmlFor="terms_conditions">
                                        Terms & Conditions
                                    </Form.Label>
                                </Form.Group>
                            </div>
                        ) : (
                            ""
                        )}
                        <div className="mb-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input pt-3 file-upload-validate"
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="bottom"
                                    title=""
                                    // name="sale_document"
                                    type="file"
                                    // id="sale_document"
                                    data-bs-original-title="Maximum file size is 2 MB."
                                    aria-label="Maximum file size is 2 MB."
                                    multiple
                                    onChange={uploadDocument}
                                    accept="image/jpg, image/jpeg, image/png, application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .docx"
                                />
                                <Form.Label className="upload-document" htmlFor="sale_document">
                                    Upload Document
                                </Form.Label>
                            </Form.Group>
                        </div>
                        <DocumentModal medias={additionalCharges?.upload_document} setAdditionalCharges={setAdditionalCharges} />
                        {!isPurchase && saleConfiguration?.footer?.is_enabled_bank_details ? 
                        
                            <div className="min-width-310px">
                                    <div className="input-group flex-nowrap">
                                    <div
                                        className={`position-relative h-40px w-100 focus-shadow pe-36px`}
                                    >
                                        <ReactSelect
                                            options={bankOptions || []}
                                            placeholder={"Select Bank"}
                                            defaultLabel={"Select Bank"}
                                            value={additionalCharges?.bank_id || null}
                                            onChange={e => handleBankChange(e)}
                                            className="h-40px"
                                            isEdit={true}
                                            handleOpen={() =>
                                                openPaymentLedgerModel(
                                                    additionalCharges?.bank_id
                                                )
                                            }
                                        />
                                    </div>
                                    <button
                                        type="button"
                                        className="input-group-text custom-group-text"
                                        onClick={() => openPaymentLedgerModel("")}
                                    >
                                        <i className="fas fa-plus text-gray-900"></i>
                                    </button>
                                </div>
                            </div>
                       
                        
                        
                        : null}
                    </div>
                    <div className="additional-charges additional_placeholder px-lg-10 px-sm-8 ps-6 pe-5">
                        <h5>Additional Charges</h5>
                    <div className="overflowX-auto">
                        <div className="position-relative pt-3 ps-14">
                            {additionalCharges?.additional_detail?.map((charge, index) => (
                                <div
                                    key={index}
                                    className="mb-4 position-relative d-flex gap-4 align-items-center justify-content-between"
                                >
                                    <div
                                        style={{
                                            minWidth: "45px",
                                            maxWidth: "45px",
                                            position: "absolute",
                                            left: "-3.5rem",
                                        }}
                                    >
                                        <div className="d-flex align-items-center position-relative">
                                            {index ===
                                                additionalCharges?.additional_detail.length - 1 &&
                                                additionalCharges?.additional_detail.length < 3 && (
                                                    <button
                                                        className="btn btn-transparent p-0 me-3"
                                                        style={{
                                                            fontSize: "20px",
                                                        }}
                                                        onClick={handleAddCharge}
                                                        type="button"
                                                    >
                                                        +
                                                    </button>
                                                )}
                                            {additionalCharges?.additional_detail.length > 1 && (
                                                <button
                                                    style={{ marginLeft: "22px" }}
                                                    className="btn btn-transparent p-0  position-absolute left-0"
                                                    type="button"
                                                    onClick={() => handleRemoveCharge(index)}
                                                >
                                                    <i className="fas fa-trash-alt text-danger"></i>
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                    <div
                                        style={{
                                            minWidth: "240px",
                                            maxWidth: "240px",
                                        }}
                                    >
                                        <div className="form-group-select mb-0">
                                            <Form.Group >
                                                <div className="input-group flex-nowrap bg-white">
                                                    <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_ledger_master ? 'pe-36px' : ""}`}>
                                                        <ReactSelect
                                                            options={charge?.is_status ? additionalChargesLedgersOption : additionalChargesDefaultLedgersOption}
                                                            value={charge?.ac_ledger_id || null}
                                                            onChange={e => {
                                                                setisFieldsChanges(true);
                                                                handleChargeChange(
                                                                    index,
                                                                    "ac_ledger_id",
                                                                    e.value,
                                                                    e
                                                                );
                                                            }
                                                            }
                                                            placeholder="Ledger"
                                                            defaultLabel="Select Ledger"
                                                            className="h-40px"
                                                            islabel={true}
                                                            portal={true}
                                                            radius={true}
                                                            bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                            isEdit={userPermission?.edit_ledger_master}
                                                            required={
                                                                additionalCharges?.additional_detail
                                                                    ?.length > 1 || charge?.ac_value
                                                            }
                                                            handleOpen={value =>
                                                                handleOpenAddLedger(
                                                                    value,
                                                                    "additional",
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                    {userPermission?.add_ledger_master ?
                                                    <button
                                                        className="input-group-text custom-group-text"
                                                        type="button"
                                                        onClick={() =>
                                                            handleOpenAddLedger(
                                                                "",
                                                                "additional",
                                                                index
                                                            )
                                                        }
                                                    >
                                                        <i className="fas fa-plus text-gray-900"></i>
                                                    </button>
                                                    : ""}
                                                </div>
                                            </Form.Group>
                                        </div>
                                    </div>
                                    <div
                                        style={{
                                            minWidth: "130px",
                                            maxWidth: "180px",
                                        }}
                                    >
                                        <div className="d-flex flex-nowrap h-40px additional_discount form-control p-0 input-group position-relative border-0">
                                            <div className="discount">
                                                <ReactSelect
                                                    defaultValue={1}
                                                    options={discountOption}
                                                    value={charge?.ac_type || null}
                                                    onChange={option => {
                                                        handleChargeChange(
                                                            index,
                                                            "ac_type",
                                                            option.value
                                                        );
                                                    }}
                                                    placeholder=""
                                                    showbg={true}
                                                    showborder={false}
                                                    bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                    height="32px"
                                                />
                                            </div>
                                            <div className="position-relative form-floating-group focus-shadow">
                                                <input
                                                    style={{
                                                        paddingLeft: "55px",
                                                    }}
                                                    placeholder=""
                                                    autoComplete="off"
                                                    type="number"
                                                    step={0.001}
                                                    className="floating-label-input form-control text-end bg-transparent border-0"
                                                    required={
                                                        additionalCharges?.additional_detail
                                                            ?.length > 1 || charge?.ac_ledger_id
                                                    }
                                                    onClick={e => e.target.select()}
                                                    value={charge?.ac_value ?? ''}
                                                    onChange={option => {
                                                        let inputValue = option.target.value;

                                                        if (
                                                            charge?.ac_type == 2 &&
                                                            inputValue > 100
                                                        ) {
                                                            inputValue = 0;
                                                        }
                                                        handleChargeChange(
                                                            index,
                                                            "ac_value",
                                                            inputValue
                                                        );
                                                    }}
                                                />
                                                <label
                                                    className="form-label amount"
                                                    htmlFor="creditPeriod"
                                                    // style={{
                                                    // maxWidth:
                                                    //     "calc(100% - 15px)",
                                                    // top: "5px",
                                                    // left: "35px",
                                                    // }}
                                                >
                                                    Amount
                                                    {charge?.ac_ledger_id && (
                                                        <span
                                                            style={{
                                                                color: "red",
                                                                paddingLeft: "4px",
                                                            }}
                                                        >
                                                            *
                                                        </span>
                                                    )}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    {company?.company?.is_gst_applicable ? (
                                        <div className="input-group w-auto bg-white">
                                            <div
                                                style={{
                                                    minWidth: "120px",
                                                }}
                                                className="select-gst focus-shadow"
                                            >
                                                <ReactSelect
                                                    options={gstOptions}
                                                    value={charge?.ac_gst_rate_id?.value || null}
                                                    onChange={option => {
                                                        handleChargeChange(
                                                            index,
                                                            "ac_gst_rate_id",
                                                            option
                                                        );
                                                    }}
                                                    placeholder="GST"
                                                    defaultLabel="Select GST"
                                                    bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                />
                                            </div>
                                        </div>
                                    ) : (
                                        <div
                                            style={{
                                                minWidth: "120px",
                                            }}
                                        ></div>
                                    )}
                                    <div className="text-end pe-1" style={{ minWidth: "94px" }}>
                                        <div className="text-end fw-bolder">
                                            {company?.company?.currentCurrencySymbol}{" "}
                                            {parseFloat(
                                                charge?.ac_type == 1
                                                    ? charge.ac_value == "-"
                                                        ? 0
                                                        : customToFixed(charge.ac_value, 2) || 0
                                                    : charge.ac_total || 0
                                            )?.toFixed(2)}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        </div>
                        <div className="mt-3">
                            <p className="fw-bolder fs-5 d-flex justify-content-between mb-4">
                                Taxable Value
                                <span className="text-primary fw-bolder pe-1">
                                    {company?.company?.currentCurrencySymbol}{" "}
                                    {customToFixed(taxableValue || 0, 2)}
                                </span>
                            </p>
                            {company?.company?.is_gst_applicable ? (
                                <>
                                    <p className="fw-bolder d-flex justify-content-between mb-2">
                                        CGST{" "}
                                        <span className="pe-1">
                                            {company?.company?.currentCurrencySymbol}{" "}
                                            {(!classification.rcm_applicable || isPurchase) &&
                                            !isIGSTCalculation &&
                                            isSGSTCalculation
                                                ? customToFixed(
                                                      !isEditCalculation
                                                          ? gstValue?.cgstValue
                                                          : gstValue?.cgstValue +
                                                                parseFloat(additionalGst || 0),
                                                      2
                                                  )
                                                : "0.00"}
                                        </span>
                                    </p>
                                    <p className="fw-bolder d-flex justify-content-between mb-2">
                                        SGST{" "}
                                        <span className="pe-1">
                                            {company?.company?.currentCurrencySymbol}{" "}
                                            {(!classification.rcm_applicable || isPurchase) &&
                                            !isIGSTCalculation &&
                                            isSGSTCalculation
                                                ? customToFixed(
                                                      !isEditCalculation
                                                          ? parseFloat(gstValue?.sgstValue)
                                                          : parseFloat(gstValue?.sgstValue) +
                                                                parseFloat(additionalGst || 0),
                                                      2
                                                  )
                                                : "0.00"}
                                        </span>
                                    </p>
                                    <p className="fw-bolder d-flex justify-content-between mb-2">
                                        IGST{" "}
                                        <span className="pe-1">
                                            {company?.company?.currentCurrencySymbol}{" "}
                                            {(!classification.rcm_applicable || isPurchase) &&
                                            isIGSTCalculation
                                                ? customToFixed(
                                                      !isEditCalculation
                                                          ? parseFloat(gstValue?.igstValue)
                                                          : parseFloat(gstValue?.igstValue) +
                                                                parseFloat(additionalGst || 0),
                                                      2
                                                  )
                                                : "0.00"}
                                        </span>
                                    </p>
                                    <p className="fw-bolder d-flex justify-content-between">
                                        CESS{" "}
                                        <span className="pe-1">
                                            <input
                                                type="number"
                                                className="text-end form-control"
                                                // placeholder="0.00"
                                                step="0.01"
                                                min="0"
                                                onClick={e => e.target.select()}
                                                value={cessValue}
                                                onChange={e =>
                                                    handleCessChange(
                                                        parseFloat(e.target.value || null)
                                                    )
                                                }
                                            />
                                        </span>
                                    </p>
                                </>
                            ) : null}
                            {saleConfiguration?.footer?.is_enabled_tcs_details ? (
                            <div className="overflowX-auto">
                                <div className="d-flex align-items-center justify-content-between gap-4 my-4 ps-14">
                                    <div className="form-group-select mb-0">
                                        <Form.Group>
                                            <div
                                                className="input-group flex-nowrap bg-white "
                                                style={{ minWidth: "240px", maxWidth: "240px" }}
                                            >
                                                <div className="position-relative h-40px w-100 pe-36px tcs-input focus-shadow overflow-text-input">
                                                    <ReactSelect
                                                        options={tcsOptions}
                                                        placeholder="TCS Ledger"
                                                        defaultLabel="Select TCS Ledger"
                                                        value={tcsRate.tcs_tax_id || null}
                                                        onChange={option =>
                                                            handleTcsChange(
                                                                "tcs_tax_id",
                                                                option.value
                                                            )
                                                        }
                                                        portal={true}
                                                        bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                        isEdit={true}
                                                        handleOpen={value =>
                                                            handleOpenAddLedger(value, "tcs")
                                                        }
                                                    />
                                                </div>
                                                <button
                                                    className="input-group-text custom-group-text"
                                                    type="button"
                                                    onClick={value =>
                                                        handleOpenAddLedger("", "tcs")
                                                    }
                                                >
                                                    <i className="fas fa-plus text-gray-900"></i>
                                                </button>
                                            </div>
                                        </Form.Group>
                                    </div>

                                    <div
                                        style={{
                                            minWidth: "150px",
                                            maxWidth: "190px",
                                        }}
                                    >
                                        <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                className="floating-label-input-2"
                                                type="number"
                                                placeholder=""
                                                min={0}
                                                max={100}
                                                step="0.01"
                                                value={tcsRate.tcs_rate}
                                                required={tcsRate.tcs_tax_id}
                                                onClick={e => e.target.select()}
                                                onChange={e =>
                                                    handleTcsChange(
                                                        "tcs_rate",
                                                        parseFloat(e.target.value)
                                                    )
                                                }
                                            />
                                            <Form.Label htmlFor="terms_conditions">
                                                TCS Rate
                                            </Form.Label>
                                        </Form.Group>
                                    </div>
                                    <div
                                        style={{
                                            minWidth: "130px",
                                            maxWidth: "190px",
                                        }}
                                        className="pe-1"
                                    >
                                        <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                className="floating-label-input-2"
                                                type="number"
                                                placeholder=""
                                                min={0}
                                                step="0.01"
                                                onClick={e => e.target.select()}
                                                value={parseFloat(tcsRate?.tcs_amount)}
                                                onChange={e =>
                                                    handleTcsChange(
                                                        "tcs_amount",
                                                        parseFloat(e.target.value)
                                                    )
                                                }
                                            />
                                            <Form.Label htmlFor="terms_conditions">
                                                TCS Amount
                                            </Form.Label>
                                        </Form.Group>
                                    </div>
                                    </div>
                                </div>
                            ) : (
                                ""
                            )}
                            <h5 className="mb-0">Add / Less</h5>
                            <div className="overflowX-auto">
                            <div
                                className={`position-relative ps-14 ${
                                    addLessChanges?.length == 3 ? "mt-3" : ""
                                }`}
                            >
                                {addLessChanges?.length > 0 &&
                                    addLessChanges?.map((item, index) => (
                                        <>
                                            <div key={index} className="d-flex align-items-center justify-content-between">
                                                <div className="my-4 position-relative d-flex gap-7 align-items-center justify-content-between">
                                                    <div
                                                        style={{
                                                            minWidth: "45px",
                                                            maxWidth: "45px",
                                                            position: "absolute",
                                                            left: "-3.5rem",
                                                        }}
                                                    >
                                                        <div className="d-flex align-items-center position-relative">
                                                            {index === addLessChanges.length - 1 &&
                                                                addLessChanges.length < 3 && (
                                                                    <button
                                                                        className="btn btn-transparent p-0 me-3"
                                                                        style={{
                                                                            fontSize: "20px",
                                                                        }}
                                                                        onClick={handleAddTax}
                                                                        type="button"
                                                                    >
                                                                        +
                                                                    </button>
                                                                )}
                                                            {addLessChanges.length > 1 && (
                                                                <button
                                                                    style={{ marginLeft: "22px" }}
                                                                    className="btn btn-transparent p-0 position-absolute left-0"
                                                                    type="button"
                                                                    onClick={() =>
                                                                        handleRemoveTax(index)
                                                                    }
                                                                >
                                                                    <i className="fas fa-trash-alt text-danger"></i>
                                                                </button>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div
                                                        style={{
                                                            minWidth: "240px",
                                                            maxWidth: "240px",
                                                        }}
                                                    >
                                                        <div className="form-group-select mb-0">
                                                            <Form.Group >
                                                                <div className="input-group flex-nowrap bg-white">
                                                                    <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_ledger_master ? "pe-36px" : ""}`}>
                                                                        <ReactSelect
                                                                            options={
                                                                                item.is_status ? addLessLedgersOption : addLessLedgersDefaultOption
                                                                            }
                                                                            value={
                                                                                item.al_ledger_id ||
                                                                                null
                                                                            }
                                                                            onChange={option =>
                                                                                handleAddLessChange(
                                                                                    index,
                                                                                    "al_ledger_id",
                                                                                    option.value
                                                                                )
                                                                            }
                                                                            placeholder="Ledger"
                                                                            defaultLabel="Select Ledger"
                                                                            className="h-40px"
                                                                            radius={true}
                                                                            portal={true}
                                                                            bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                                            isEdit={userPermission?.edit_ledger_master}
                                                                            handleOpen={value =>
                                                                                handleOpenAddLedger(
                                                                                    value,
                                                                                    "addless",
                                                                                    index
                                                                                )
                                                                            }
                                                                            required={
                                                                                addLessChanges?.length >
                                                                                    1 ||
                                                                                item?.al_value
                                                                            }
                                                                        />
                                                                    </div>
                                                                    {userPermission?.add_ledger_master ?
                                                                    <button
                                                                        type="button"
                                                                        onClick={() =>
                                                                            handleOpenAddLedger(
                                                                                "",
                                                                                "addless",
                                                                                index
                                                                            )
                                                                        }
                                                                        className="input-group-text custom-group-text"
                                                                    >
                                                                        <i className="fas fa-plus text-gray-900"></i>
                                                                    </button>
                                                                    : ""}
                                                                </div>
                                                            </Form.Group>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="mb-4 position-relative d-flex gap-4 align-items-center justify-content-end">
                                                    <div className="form-check form-check-custom mx-2">
                                                        <input
                                                            className="form-check-input"
                                                            type="checkbox"
                                                            value={
                                                                item.al_is_show_in_print == 1
                                                                    ? 1
                                                                    : 0
                                                            }
                                                            checked={
                                                                item.al_is_show_in_print == 1
                                                                    ? 1
                                                                    : 0
                                                            }
                                                            onChange={e =>
                                                                handleAddLessChange(
                                                                    index,
                                                                    "al_is_show_in_print",
                                                                    e.target.checked
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                    <Print />
                                                    <div
                                                        className="d-flex"
                                                        style={{
                                                            minWidth: "120px",
                                                        }}
                                                    >
                                                        <div
                                                            style={{
                                                                minWidth: "130px",
                                                                maxWidth: "190px",
                                                            }}
                                                            className="pe-1"
                                                        >
                                                            <div className="d-flex flex-nowrap h-40px additional_discount form-control p-0 input-group position-relative border-0">
                                                                <div className="discount">
                                                                    <ReactSelect
                                                                        defaultValue={1}
                                                                        options={discountOption}
                                                                        value={item.al_type || null}
                                                                        onChange={e => {
                                                                            setisFieldsChanges(true);
                                                                            handleAddLessChange(
                                                                                index,
                                                                                "al_value",
                                                                                ""
                                                                            );
                                                                            handleAddLessChange(
                                                                                index,
                                                                                "al_type",
                                                                                e.value
                                                                            );
                                                                        }}
                                                                        placeholder=""
                                                                        isCreatable={false}
                                                                        showbg={true}
                                                                        showborder={false}
                                                                        height="30px"
                                                                        bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                                    />
                                                                </div>
                                                                <div className=" focus-shadow">
                                                                    <input
                                                                        className="form-control text-end border-0"
                                                                        name="discount_value"
                                                                        type="text"
                                                                        required={
                                                                            addLessChanges?.length >
                                                                                1 || item.al_ledger_id
                                                                        }
                                                                        value={item.al_value}
                                                                        onClick={e => e.target.select()}
                                                                        onChange={e =>
                                                                            handleAddLessChange(
                                                                                index,
                                                                                "al_value",
                                                                                e.target.value,
                                                                            )
                                                                        }
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    ))}
                            </div>
                            </div>

                            <div className="d-flex align-items-center justify-content-between gap-12 mb-4">
                                <div>
                                    <p className="fw-bolder mb-0">Round off Amount</p>
                                </div>
                                <div className="pe-1">
                                    <input
                                        type="number"
                                        className="text-end form-control"
                                        step="0.01"
                                        // placeholder="0.00"
                                        onClick={e => e.target.select()}
                                        value={gstCalculation.round_of_amount}
                                        onChange={e => handleRoundOffChange(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="fw-bolder fs-5 d-flex justify-content-between align-items-center">
                                <h5 className="fw-bolder mb-0">
                                    Grand Total ({company?.company?.currentCurrencySymbol})
                                </h5>
                                <span className="text-primary fw-bolder pe-1">
                                    {company?.company?.currentCurrencySymbol}{" "}
                                    {customToFixed(finalAmount, 2) || 0.0}
                                </span>
                            </div>
                            {otherDetail.credit_limit && showCreditLimit ? (
                                <div className="d-flex align-items-center mt-3 justify-content-between">
                                    <div>
                                        <h5 className="text-danger mb-0">Credit Limit</h5>
                                    </div>

                                    <div className="pe-1">
                                        <div className="text-end">
                                            <span className="fw-bolder text-danger">
                                                {company?.company?.currentCurrencySymbol}{" "}
                                                {otherDetail?.credit_limit_amount ?? 0}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ) : null}
                        </div>
                    </div>
                </div>
                {isShipLedgerModel && (
                    <AddLedgerModal
                        ledgerDetailOptions={ledgerDetailOptions}
                        show={isShipLedgerModel}
                        handleClose={closeShipLedgerModel}
                        sale={false}
                        name={ledgerModelName}
                        gstQuote={gstQuote}
                        setLedgerModelName={setLedgerModelName}
                        entityType={ledgerEntityType}
                        ledgerGstOption={ledgerGstOption}
                        ledgerGroupType={ledgerGroupType}
                        itemLegder={itemLegder}
                        getId={showCurrentData}
                        index={changeLedgerIndex.index}
                        changeLedgerName={changeLedgerIndex.name}
                        addLessChanges={addLessChanges}
                        setAddLessChanges={setAddLessChanges}
                        additionalCharges={additionalCharges}
                        setAdditionalCharges={setAdditionalCharges}
                        tcsRate={tcsRate}
                        setTcsRate={setTcsRate}
                        action={currentAction}
                        isPurchase={isPurchase}
                        taxableValue={taxableValue}
                        invoiceValue={invoiceValue}
                        partyLedgerId={gstQuote?.party_ledger_id}
                    />

                )}
                {openLedgerModel && (
                    <AddLedgerModal
                        show={openLedgerModel}
                        handleClose={closeBankLedgerModel}
                        id={1}
                        sale={false}
                        name={bankLedgerModelName}
                        setLedgerModelName={setLedgerModelName}
                        action={currentAction}
                        gstQuote={gstQuote}
                        itemType={itemType}
                        ledgerDetailOptions={ledgerDetailOptions}
                        setAdditionalCharges={setAdditionalCharges}
                        isBankDetails={true}
                    />
                )}
                {isRecurringDynamicNote && <DynamicNoteDescriptionModal
                    isRecurringDynamicNote={isRecurringDynamicNote}
                    handleDescriptionClose={() => setIsRecurringDynamicNote(false)}
                    noteCount={noteCount}
                    setNoteCount={setNoteCount}
                    additionalCharges={additionalCharges}
                    setAdditionalCharges={setAdditionalCharges}
                />}
            </>
        );
    }
);

export default ShippingCard;
