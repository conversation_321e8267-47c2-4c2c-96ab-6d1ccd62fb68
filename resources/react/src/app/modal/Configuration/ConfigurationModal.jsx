import React, { useContext, useEffect, useMemo, useState } from "react";
import { Form, Row, Col, Modal } from "react-bootstrap";
import ReactSelect from "../../../components/ui/ReactSelect";
import { StateContext } from "../../../context/StateContext";
import { useDispatch, useSelector } from "react-redux";
import useDropdownOption from "../../../shared/dropdownList";
import { FormInput } from "../../../components/ui/Input";
import Setting from "../../../assets/images/svg/setting";
import {
    deleteCustomHeaderField,
    deleteItemCustomFieldStatus,
    fetchBankList,
    fetchCustomFieldList,
    getCustomField,
    getItemCustomField,
    updateConfiguration,
    updateCustomFieldStatus,
    updateTransactionStatusCustomField,
    updateItemStatusCustomField,
    updateRearrangeItemList,
    updateSingleConfiguration,
    fetchItemMasterCustomFieldList,
    getItemMasterCustomField,
} from "../../../store/configuration/configurationSlice";
import { fetchLedgerById, fetchLedgerGroupList, fetchPartyDetail, getLedgerModelDetail } from "../../../store/ledger/ledgerSlice";
import AddCustomField from "./AddCustomField";
import RearrangeItemModal from "./RearrangeItemModal";
import { checkPathName } from "../../../shared/sharedFunction";
import TermsAndConditionsModal from "./TermsAndConditionsModal";
import DefaultTitleModal from "./DefaultTitleModal";
import {
    apiBaseURL,
    CUSTOM_FIELD_ITEM_TRANSACTION_TYPE,
    LedgerType,
    toastType,
    TRANSACTION_TYPE,
} from "../../../constants";
import WarningModal from "../../common/WarningModal";
import { handleShortcutKeys } from "../../../shared/shortcut-keys";
import CustomFormulaModal from "./CustomFormulaModal";
import AddLedgerModal from "../Ledger/LedgerModal";
import { errorToast } from "../../../store/actions/toastAction";
import AddItemMasterCustomFieldModal from "../CustomFieldItemsMaster/AddItemMasterCustomField";

const relevantPaths = [
    "income-estimate-quote",
    "sales",
    "sale-returns",
    "income-debit-notes",
    "income-credit-notes",
    "sales-create",
    "create-sale-return",
    "sale-returns-create",
    "income-estimate-quote-create",
    "income-debit-notes-create",
    "income-credit-notes-create",
  ];

const ConfigurationModal = ({ roundOffOption, isRecurring = false }) => {
    const dispatch = useDispatch();
    const { company } = useSelector(state => state);
    const tableHeader = useSelector(state => state.table?.tableHeader);
    const currentFinancialYear = company?.company?.currentFinancialYear;
    const isEstimateTransaction = window.location.pathname.includes("income-estimate-quote");
    const isPurchaseOrderTransaction = window.location.pathname.includes("purchase-order");
    const [isTermsConditions, setIsTermsConditions] = useState(false);
    const [isDefaultTitleModal, setIsDefaultTitleModal] = useState(false);
    const [isDeleteModal, setIsDeleteModal] = useState(false);
    const [customFieldFormula, setCustomFieldFormula] = useState("");
    const [openLedgerModel, setOpenLedgerModel] = useState(false)
    const [ledgerModelName, setLedgerModelName] = useState("");
    const [currentAction, setCurrentAction] = useState("");
    const [openCfItemModal, setOpenCfItemModal] = useState(false);

    const isBankDetails = relevantPaths.some(path => window.location.pathname.includes(path));

    const closeBankLedgerModel = () =>{
        setOpenLedgerModel(false)
    }

    const getCustomItemTransactionType = checkPathName("/sales")
    ? TRANSACTION_TYPE.SALE
    : checkPathName("/sale-returns")
    ? TRANSACTION_TYPE.SALE_RETURN
    : checkPathName("/create-sale-return")
    ? TRANSACTION_TYPE.SALE_RETURN
    : checkPathName("/purchase-sale")
    ? TRANSACTION_TYPE.SALE
        : checkPathName("/income-estimate-quote")
        ? TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE
        : checkPathName("delivery-challan")
        ? TRANSACTION_TYPE.DELIVERY_CHALLAN
        : checkPathName("/income-debit-notes")
        ? TRANSACTION_TYPE.INCOME_DEBIT_NOTE
        : checkPathName("/income-credit-notes")
        ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE
        : checkPathName("/purchase-order")
        ? TRANSACTION_TYPE.PURCHASE_ORDER
        : checkPathName("/purchases")
        ? TRANSACTION_TYPE.PURCHASE
        : checkPathName("/import-documents")
        ? TRANSACTION_TYPE.PURCHASE
        : checkPathName("/purchase-create")
        ? TRANSACTION_TYPE.PURCHASE
        : checkPathName("/purchase-returns")
        ? TRANSACTION_TYPE.PURCHASE_RETURN
        : checkPathName("create-purchase-return")
        ? TRANSACTION_TYPE.PURCHASE_RETURN
        : checkPathName("expense-credit-notes")
        ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE
        : checkPathName("/recurring-invoices")
        ? TRANSACTION_TYPE.RECURRING
        : checkPathName("/expense-debit-notes")
        ? TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
        : checkPathName("expense-debit-notes-create")
        ? TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
        : "";
    const getTransactionType = checkPathName("/sales")
    ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE
    : checkPathName("/sale-returns")
    ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE_RETURN
        : checkPathName("/purchase-sale")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE
        : checkPathName("/create-sale-return")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE_RETURN
        : checkPathName("/income-estimate-quote")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE
        : checkPathName("delivery-challan")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.DELIVERY_CHALLAN
        : checkPathName("/income-debit-notes")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_DEBIT_NOTE
        : checkPathName("/income-credit-notes")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_CREDIT_NOTE
        : checkPathName("/purchase-order")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE_ORDER
        : checkPathName("/purchases")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE
        : checkPathName("/import-documents")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE
        : checkPathName("/purchase-create")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE
        : checkPathName("/purchase-return")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE_RETURN
        : checkPathName("create-purchase-return")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE_RETURN
        : checkPathName("expense-credit-notes")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE
        : checkPathName("/recurring-invoices")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.RECURRING
        : checkPathName("/expense-debit-notes")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
        : checkPathName("expense-debit-notes-create")
        ? CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
        : "";
    const configurationUrlForUpdateField =
        getTransactionType == 1
            ? apiBaseURL.SALE_CONFIGURATION
            : getTransactionType == 2
            ? apiBaseURL.SALE_RETURN_CONFIGURATION
            : getTransactionType == 15
            ? apiBaseURL.ESTIMATE_CONFIGURATION
            : getTransactionType == 4
            ? apiBaseURL.INCOME_CREDIT_NOTE_CONFIGURATION
            : getTransactionType == 3
            ? apiBaseURL.INCOME_DEBIT_NOTE_CONFIGURATION
            : getTransactionType == 16
            ? apiBaseURL.DELIVERY_CHALLAN_CONFIGURATION
            : getTransactionType == 17
            ? apiBaseURL.PURCHASE_ORDER_CONFIGURATION
            : getTransactionType == 5
            ? apiBaseURL.PURCHASE_CONFIGURATION
            : getTransactionType == 6
            ? apiBaseURL.PURCHASE_RETURN_CONFIGURATION
            : getTransactionType == 7
            ? apiBaseURL.EXPENSE_DEBIT_NOTE_CONFIGURATION
            : getTransactionType == 18
            ? apiBaseURL.RECURRING_INVOICE_CONFIGURATION
            : getTransactionType == 8
            ? apiBaseURL.EXPENSE_CREDIT_NOTE_CONFIGURATION
            : "";
    const [configurationSale, setConfigurationSale] = useState({
        document: {
            method_of_voucher_number: "",
            start_with_number: "",
            is_change_every_financial_year: "",
            prefix: "",
            prefix_method: 1,
            suffix_method: 1,
            suffix: "",
            bank_id: "",
            title_of_print: "",
            terms_and_conditions: "",
            default_title: "",
            apply_income_bank_details: 0,
        },
        header: {
            is_change_gst_details: false,
            is_enabled_po_details_of_buyer: false,
            is_enabled_broker_details: false,
            is_enabled_estimate_quote: false,
            is_enabled_delivery_challan: false,
            is_enabled_shipping_address: false,
            is_enabled_transport_details: false,
            is_enabled_credit_period_details: false,
            is_enable_narration: false,
            is_enabled_mrp: false,
            is_enabled_dispatch_details: false,
            is_enabled_eway_details: false,
            is_enabled_phone_number: false,
        },
        item_table_configuration: {
            consolidating_items_to_invoice: false,
            is_additional_item_description: false,
            is_additional_ledger_description: false,
            warn_on_negative_stock: false,
            is_enabled_discount_2: false,
            is_enable_free_quantity: false,
        },
        footer: {
            is_enable_narration: false,
            is_enabled_tcs_details: false,
            round_off_method: 1,
            is_enabled_terms_and_conditions: false,
            is_enabled_payment_details: false,
            is_enabled_tds_details: false,
            is_enabled_bank_details: false,
            on_pay_ledger_id: "",
        },
    });
    const {
        isSaleModel,
        openSaleModel,
        closeSaleModel,
        configurationModalName,
        configurationHeaderList,
        configurationURL,
        configurationTableList,
        configurationFooterList,
        gstQuote,
        setGstQuote,
        setIsEditCalculation,
        customHeaderList,
        setCustomHeaderList,
        openCustomFieldModel,
        closeRearrangeModel,
        getSingleCustomHeader,
        setGetSingleCustomHeader,
        setIsCheckGstType,
        isEditModel,
        setIsEditModel,
        setIsrenderDuplicateTitle,
        invoiceDetail,
        itemTableCustomFieldList,
        setItemTableCustomFieldList,
        isRearrangeCustomModel,
        openIsRearrangeCustomModel,
        getSingleCustomField,
        setGetSingleCustomField,
        itemType,
        dragHeaderList,
        isDeleteCustomItem,
        setIsDeleteCustomItem,
        isCustomFormula,
        openCustomFormulaModel,
        closeCustomFormulaModel,
        setCheckGroupLedgerType,
        setAdditionalCharges,
        setCustomFieldItemMaster
    } = useContext(StateContext);
    const {
        vouchersOption,
        bankOptions,
        estimateQuoteTitleOptions,
        purchaseOrderOptions,
        ledgerDetailOptions
    } = useDropdownOption();
    const saleConfiguration = useSelector(state => state.configuration?.configuration);
    const shortcutConfiguration = event => {
        if (event.altKey && event.keyCode == 123) {
            return openSaleModel();
        }
        if (event.key === "Escape") {
            closeRearrangeModel();
            return closeSaleModel();
        }
    };
    handleShortcutKeys(shortcutConfiguration);

    useEffect(() => {
        if (isSaleModel) {
            document.body.classList.add("enable-scroll");
        } else {
            document.body.classList.remove("enable-scroll");
        }
    }, [isSaleModel]);

    useEffect(() => {
        if (isSaleModel === true) {
            setConfigurationSale({
                document: {
                    method_of_voucher_number:
                        saleConfiguration?.document_prefix?.method_of_voucher_number,
                    start_with_number: saleConfiguration?.document_prefix?.start_with_number,
                    is_change_every_financial_year:
                        saleConfiguration?.document_prefix?.change_every_financial_year,
                    prefix: saleConfiguration?.document_prefix?.prefix
                        ? saleConfiguration?.document_prefix?.prefix
                        : "",
                    prefix_method: saleConfiguration?.document_prefix?.prefix_method,
                    suffix: saleConfiguration?.document_prefix?.suffix
                        ? saleConfiguration?.document_prefix?.suffix
                        : "",
                    suffix_method: saleConfiguration?.document_prefix?.suffix_method,
                    bank_id: saleConfiguration?.document_prefix?.bank_id,
                    title_of_print: saleConfiguration?.document_prefix?.title_of_print,
                    default_title: saleConfiguration?.document_prefix?.default_title,
                    terms_and_conditions: saleConfiguration?.document_prefix?.terms_and_conditions,
                },
                header: {
                    is_enabled_broker_details: saleConfiguration?.header?.is_enabled_broker_details,
                    is_enabled_estimate_quote: saleConfiguration?.header?.is_enabled_estimate_quote,
                    is_enabled_delivery_challan:
                        saleConfiguration?.header?.is_enabled_delivery_challan,
                    is_enabled_dispatch_details:
                        saleConfiguration?.header?.is_enabled_dispatch_details,
                    is_enabled_shipping_address:
                        saleConfiguration?.header?.is_enabled_shipping_address,
                    is_enabled_po_details_of_buyer:
                        saleConfiguration?.header?.is_enabled_po_details_of_buyer,
                    is_enabled_eway_details: saleConfiguration?.header?.is_enabled_eway_details,
                    is_enabled_transport_details:
                        saleConfiguration?.header?.is_enabled_transport_details,
                    is_change_gst_details: saleConfiguration?.header?.is_change_gst_details,
                    is_enabled_po_details: saleConfiguration?.header?.is_enabled_po_details,
                    is_enabled_credit_period_details:
                        saleConfiguration?.header?.is_enabled_credit_period_details,
                    is_enabled_phone_number: saleConfiguration?.header?.is_enabled_phone_number,
                },
                item_table_configuration: {
                    consolidating_items_to_invoice:
                        saleConfiguration?.item_table_configuration?.consolidating_items_to_invoice,
                    is_additional_item_description:
                        saleConfiguration?.item_table_configuration?.is_additional_item_description,
                    is_additional_ledger_description:
                        saleConfiguration?.item_table_configuration
                            ?.is_additional_ledger_description,
                    warn_on_negative_stock:
                        saleConfiguration?.item_table_configuration?.warn_on_negative_stock,
                    is_enabled_mrp: saleConfiguration?.item_table_configuration?.is_enabled_mrp,
                    is_enabled_hsn_code:
                        saleConfiguration?.item_table_configuration?.is_enabled_hsn_code,
                    is_enabled_discount_2:
                        saleConfiguration?.item_table_configuration?.is_enabled_discount_2,
                    is_enable_free_quantity:
                        saleConfiguration?.item_table_configuration?.is_enable_free_quantity,
                },
                footer: {
                    is_enable_narration: saleConfiguration?.footer?.is_enable_narration,
                    is_enabled_tcs_details: saleConfiguration?.footer?.is_enabled_tcs_details,
                    is_enabled_terms_and_conditions:
                        saleConfiguration?.footer?.is_enabled_terms_and_conditions,
                    is_enabled_payment_details:
                        saleConfiguration?.footer?.is_enabled_payment_details,
                    round_off_method: saleConfiguration?.footer?.round_off_method,
                    is_enabled_bank_details: saleConfiguration?.footer?.is_enabled_bank_details,
                    is_enabled_tds_details: saleConfiguration?.footer?.is_enabled_tds_details,
                    on_pay_ledger_id: saleConfiguration?.footer?.on_pay_ledger_id || null,
                },
            });
            setCustomHeaderList(saleConfiguration?.header?.custom_fields);
            dispatch(fetchBankList({ids: [saleConfiguration?.document_prefix?.bank_id]}));
        }
    }, [saleConfiguration, isSaleModel]);

    const handleDeleteCustomHeaderField = () => {
        dispatch(
            deleteCustomHeaderField(
                getSingleCustomHeader?.custom_field_id,
                setIsDeleteModal,
                configurationURL,
                setGetSingleCustomHeader
            )
        );
    };

    const HeaderList = useMemo(
        () =>
            [
                ...configurationHeaderList,
                ...(!isRecurring
                    ? [
                          {
                              name: "Shipping Address",
                              key: "is_enabled_shipping_address",
                              value: saleConfiguration?.header?.is_enabled_shipping_address,
                              position: 2,
                          },
                      ]
                    : []),
                {
                    name: "Broker Details",
                    key: "is_enabled_broker_details",
                    value: saleConfiguration?.header?.is_enabled_broker_details,
                    position: 5,
                },
                {
                    name: "Transport Details",
                    key: "is_enabled_transport_details",
                    value: saleConfiguration?.header?.is_enabled_transport_details,
                    position: 6,
                },
            ].sort((a, b) => a.position - b.position),
        [saleConfiguration, configurationHeaderList]
    );

    const TableList = useMemo(
        () =>
            [
                ...configurationTableList,
                {
                    name: "Consolidating Item To Invoice",
                    key: "consolidating_items_to_invoice",
                    value: saleConfiguration?.item_table_configuration
                        ?.consolidating_items_to_invoice,
                    position: 5,
                },
                ...(saleConfiguration?.item_table_configuration &&
                Object.prototype.hasOwnProperty.call(
                    saleConfiguration.item_table_configuration,
                    "is_enable_free_quantity"
                )
                    ? [
                          {
                              name: "Free Quantity",
                              key: "is_enable_free_quantity",
                              value: saleConfiguration.item_table_configuration
                                  .is_enable_free_quantity,
                              position: 2,
                          },
                          {
                              name: "Additional Item Description",
                              key: "is_additional_item_description",
                              value: saleConfiguration.item_table_configuration
                                  .is_additional_item_description,
                              position: 3,
                          },
                      ]
                    : [
                          {
                              name: "Additional Item Description",
                              key: "is_additional_item_description",
                              value: saleConfiguration?.item_table_configuration
                                  ?.is_additional_item_description,
                              position: 2,
                          },
                      ]),

                company?.company?.is_gst_applicable && {
                    name: "HSN/SAC Code",
                    key: "is_enabled_hsn_code",
                    value: saleConfiguration?.item_table_configuration?.is_enabled_hsn_code,
                    position: 4,
                },
            ]
                .sort((a, b) => a.position - b.position)
                .filter(Boolean),
        [saleConfiguration, configurationTableList]
    );

    const footerList = useMemo(
        () =>
            [
                ...configurationFooterList,
                {
                    name: "Note",
                    key: "is_enable_narration",
                    value: saleConfiguration?.footer?.is_enable_narration,
                    position: 5,
                },
                {
                    name: "Round off",
                    key: "round_off_method",
                    value: saleConfiguration?.footer?.round_off_method,
                    position: 3,
                },
                ...(isBankDetails
                    ? [
                        {
                            name: "Bank Details",
                            key: "is_enabled_bank_details",
                            value: saleConfiguration?.footer?.is_enabled_bank_details,
                            position: 6,
                        },
                    ]
                    : []),
            ].sort((a, b) => a.position - b.position),
        [saleConfiguration, configurationFooterList]
    );

    const handleSubmit = e => {
        // submit
        e.preventDefault();
        const data = {
            ...configurationSale.document,
            ...configurationSale.footer,
            ...configurationSale.header,
            ...configurationSale.item_table_configuration,
        };

        const isBankDetailsUpdate = configurationSale.document?.apply_income_bank_details === 1 ? true : false;

        dispatch(updateConfiguration(configurationURL, data, closeSaleModel, setAdditionalCharges, isBankDetailsUpdate));
        if (saleConfiguration?.document_prefix?.default_title != invoiceDetail?.default_title) {
            setIsrenderDuplicateTitle(true);
        }
    };

    const onUpdateCustomField = (type, data) => {
        setIsEditCalculation(true);
        const updateCustomField = {
            custom_field_id: data.custom_field_id,
            type: data.type,
            is_enabled: type ? 1 : 0,
        };
        dispatch(updateCustomFieldStatus(updateCustomField, configurationUrlForUpdateField));
    };

    const onToggle = (e, key) => {
        setIsEditCalculation(true);
        const updateData = (updateKey, value) => {
            if (updateKey === "round_off_method") {
                setIsCheckGstType(true);
            }
            dispatch(
                updateSingleConfiguration(
                    configurationURL,
                    updateKey === "round_off_method" || updateKey === "terms_and_conditions"
                        ? { [updateKey]: e }
                        : { [updateKey]: value },
                    closeSaleModel
                )
            );
        };

        const value = e ? 1 : 0;

        if (key === "is_enabled_delivery_challan" || key === "is_enabled_estimate_quote") {
            const oppositeKey =
                key === "is_enabled_delivery_challan"
                    ? "is_enabled_estimate_quote"
                    : "is_enabled_delivery_challan";
            if (gstQuote?.party_ledger_id) {
                dispatch(fetchPartyDetail(gstQuote?.party_ledger_id));
            }
            updateData(key, value);
            const oppositeValue = configurationSale.header[oppositeKey];
            if (oppositeValue) {
                updateData(oppositeKey, e ? 0 : 1);
            }
            setGstQuote({ ...gstQuote, quotes_id: [] });
        } else if (key === "on_pay_ledger_id") {
            updateData(key, e.value);
        } else {
            updateData(key, value);
        }
    };

    const handleOpenDeleteModal = item => {
        setIsDeleteModal(true);
        setGetSingleCustomHeader(item);
    };

    const openCustomFieldItemModal = async (item) =>{
        setOpenCfItemModal(true);
        if(item?.custom_field_id){
            setIsEditModel(true);
            const customField = await dispatch(getItemMasterCustomField(item?.custom_field_id));
            setCustomFieldItemMaster(customField);
            dispatch(fetchItemMasterCustomFieldList());
        }else{
            dispatch(fetchItemMasterCustomFieldList());
            setCustomFieldItemMaster(null);
        }
    }

    const handleCloseCfModal = () => {
        setOpenCfItemModal(false);
        setIsEditModel(false);
    }
    const handleOpenEditModel = item => {
        openCustomFieldModel();
        setIsEditModel(true);
        setGetSingleCustomHeader(item);
        dispatch(getCustomField(item.custom_field_id, getTransactionType));
        dispatch(fetchCustomFieldList());
    };

    const handleChangeDefaultTitle = e => {
        setConfigurationSale({
            ...configurationSale,
            document: {
                ...configurationSale?.document,
                default_title: e.value,
            },
        });
    };

    const openCustomField = type => {
        setIsEditModel(false);
        if (type) {
            openIsRearrangeCustomModel();
        } else {
            openCustomFieldModel();
        }
        dispatch(fetchCustomFieldList());
    };

    useEffect(() => {
        const filteredTableList = tableHeader?.length > 0 ? tableHeader?.filter(
            table =>
                table?.header !== "Item" && table?.header !== "Ledger" && table?.header !== "Total" && (table?.transaction_type)
        ) : [];
        setItemTableCustomFieldList(filteredTableList);
    }, [tableHeader]);

    const handleOpenItemEditModel = item => {
        setIsEditModel(true);
        openIsRearrangeCustomModel();
        dispatch(fetchCustomFieldList());
        dispatch(getItemCustomField(item?.custom_field_item_id, getCustomItemTransactionType));
        setGetSingleCustomField(item);
    };

    const handleOpenDeleteItemModal = item => {
        setIsDeleteCustomItem(true);
        setGetSingleCustomField(item);
    };
    const handleChangeItemToggle = item => {
        let newDetail = {
            is_enabled: !item?.is_enabled,
            custom_field_item_id: item?.custom_field_item_id,
            type: item?.transaction_type,
        };
        dispatch(updateItemStatusCustomField(newDetail, itemType == "item" ? 1 : 2));
        const updatedMetaData = dragHeaderList?.map(list => {
            if (list.custom_field_item_id === item.custom_field_item_id) {
                return {
                    ...list,
                    is_enabled: !item?.is_enabled, // toggling current value
                };
            }
            return list;
        });
        dispatch(
            updateRearrangeItemList({
                invoice_type: itemType == "item" ? 1 : 2,
                type: item?.transaction_type,
                meta: updatedMetaData,
                message: false,
            })
        );
    };
    const handleChangeItemMasterToggle = item => {
        let newDetail = {
            is_enabled: !item?.is_enabled,
            custom_field_id: item?.custom_field_id,
            transaction_type: item?.transaction_type,
        };
        dispatch(updateTransactionStatusCustomField(newDetail, itemType == "item" ? 1 : 2));
        const updatedMetaData = dragHeaderList?.map(list => {
            if (list.custom_field_id === item.custom_field_id) {
                return {
                    ...list,
                    is_enabled: !item?.is_enabled, // toggling current value
                };
            }
            return list;
        });
        dispatch(
            updateRearrangeItemList({
                invoice_type: itemType == "item" ? 1 : 2,
                type: item?.transaction_type,
                meta: updatedMetaData,
                message: false,
            })
        );
    };

    const handleConfirmDelete = () => {
        setIsDeleteCustomItem(false);
        dispatch(
            deleteItemCustomFieldStatus(
                getSingleCustomField,
                configurationUrlForUpdateField,
                getTransactionType,
                itemType == "item" ? 1 : 2
            )
        );
    };

    const handleCustomFormula = (data) =>{
        setCustomFieldFormula(data)
        openCustomFormulaModel();
    }

    const openPaymentLedgerModel = (id) => {
        if(id){
            dispatch(fetchLedgerById(id));
            setLedgerModelName({ name: "Update Ledger", id: id });
        }else{
            setLedgerModelName({ name: "Add Ledger", id: "" });
        }
        dispatch(fetchLedgerGroupList());
        dispatch(getLedgerModelDetail(LedgerType.BANK_LEDGER));
        setCheckGroupLedgerType("payment");
        setCurrentAction("Bank");
        setOpenLedgerModel(true);
    };

    const handleUpdateBankOnPastInvoices = (e) => {
        if (configurationSale?.document?.bank_id) {
            setConfigurationSale({
                ...configurationSale,
                document: {
                    ...configurationSale?.document,
                    apply_income_bank_details: e.target.checked ? 1 : 0,
                },
            })
        } else {
            dispatch(
                errorToast({
                    text: "Please Select Bank First",
                    type: toastType.ERROR,
                })
            );
        }
    };

    return (
        <>
            <div className="nav-item">
                <button className="nav-link" type="button" onClick={openSaleModel}>
                    <Setting />
                </button>
            </div>
            <Modal
                show={isSaleModel}
                onHide={closeSaleModel}
                className={`sale-configuration-modal custom-offcanvas-modal fade p-0 `}
                // centered
                // size="xl"
                // backdrop={true}
            >
                <form>
                    <div className="offcanvas-header bg-white">
                        <h3 className="mb-0">{configurationModalName}</h3>
                        <button
                            className="btn close-button p-0"
                            onClick={closeSaleModel}
                            type="button"
                        >
                            &times;
                        </button>
                    </div>
                    <div className="offcanvas-body">
                        {!isRecurring && (
                            <>
                                <h4>Document Prefixes</h4>
                                <div className="desc-box mb-5">
                                    <div className="mb-5 sales-table_discount">
                                        <ReactSelect
                                            options={vouchersOption}
                                            placeholder={"Method of Voucher Number"}
                                            value={
                                                configurationSale?.document
                                                    ?.method_of_voucher_number || null
                                            }
                                            onChange={e =>
                                                setConfigurationSale({
                                                    ...configurationSale,
                                                    document: {
                                                        ...configurationSale?.document,
                                                        method_of_voucher_number: e.value,
                                                    },
                                                })
                                            }
                                        />
                                    </div>
                                    {configurationSale?.document?.start_with_number ? (
                                        <div className="mb-5">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    value={
                                                        configurationSale?.document
                                                            ?.start_with_number
                                                    }
                                                    onChange={e =>
                                                        setConfigurationSale({
                                                            ...configurationSale,
                                                            document: {
                                                                ...configurationSale?.document,
                                                                start_with_number: e.target.value,
                                                            },
                                                        })
                                                    }
                                                />
                                                <Form.Label>Start Number</Form.Label>
                                            </Form.Group>
                                        </div>
                                    ) : null}
                                    <div className="d-flex align-items-center justify-content-between">
                                        <p className="mb-0 h5 fw-medium">
                                            Change Every Financial Year
                                        </p>
                                        <div
                                            className="form-check form-switch d-flex gap-2 my-3"
                                            style={{
                                                paddingLeft: "0",
                                            }}
                                        >
                                            <input
                                                className="form-check-input"
                                                style={{
                                                    float: "right",
                                                    marginLeft: "0",
                                                }}
                                                type="checkbox"
                                                id="isTcsApplicable"
                                                checked={
                                                    configurationSale?.document
                                                        ?.is_change_every_financial_year
                                                }
                                                onChange={e =>
                                                    onToggle(
                                                        e.target.checked,
                                                        "is_change_every_financial_year"
                                                    )
                                                }
                                            />
                                        </div>
                                    </div>
                                    {configurationSale?.document?.prefix_method ||
                                    configurationSale?.document?.prefix_method === null ? (
                                        <div className="mb-5">
                                            <Row>
                                                <Col
                                                    sm={4}
                                                    className="pe-sm-0 mb-sm-0 mb-4 sales-table_discount"
                                                >
                                                    <ReactSelect
                                                        options={[
                                                            {
                                                                value: 1,
                                                                label: "Text",
                                                            },
                                                            {
                                                                value: 2,
                                                                label: "Financial Year",
                                                            },
                                                        ]}
                                                        value={
                                                            configurationSale?.document
                                                                ?.prefix_method || null
                                                        }
                                                        onChange={e =>
                                                            setConfigurationSale({
                                                                ...configurationSale,
                                                                document: {
                                                                    ...configurationSale?.document,
                                                                    prefix_method: e.value,
                                                                    prefix:
                                                                        e.value === 2
                                                                            ? currentFinancialYear?.startYear +
                                                                              "-" +
                                                                              currentFinancialYear?.endYear
                                                                            : "",
                                                                },
                                                            })
                                                        }
                                                        placeholder={"Prefix"}
                                                        defaultLabel={"Select Prefix"}
                                                    />
                                                </Col>
                                                <Col sm={8}>
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input"
                                                            type="text"
                                                            placeholder=""
                                                            value={
                                                                configurationSale?.document?.prefix
                                                            }
                                                            onChange={e =>
                                                                setConfigurationSale({
                                                                    ...configurationSale,
                                                                    document: {
                                                                        ...configurationSale?.document,
                                                                        prefix: e.target.value,
                                                                    },
                                                                })
                                                            }
                                                        />
                                                        <Form.Label>Prefix</Form.Label>
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : null}
                                    {configurationSale?.document?.suffix_method ||
                                    configurationSale?.document?.suffix_method === null ? (
                                        <div className="mb-5">
                                            <Row>
                                                <Col
                                                    sm={4}
                                                    className="pe-sm-0 mb-sm-0 mb-4 sales-table_discount"
                                                >
                                                    <ReactSelect
                                                        options={[
                                                            {
                                                                value: 1,
                                                                label: "Text",
                                                            },
                                                            {
                                                                value: 2,
                                                                label: "Financial Year",
                                                            },
                                                        ]}
                                                        placeholder={"Suffix"}
                                                        defaultLabel={"Select Suffix"}
                                                        value={
                                                            configurationSale?.document
                                                                ?.suffix_method || null
                                                        }
                                                        onChange={e =>
                                                            setConfigurationSale({
                                                                ...configurationSale,
                                                                document: {
                                                                    ...configurationSale?.document,
                                                                    suffix_method: e.value,
                                                                    suffix:
                                                                        e.value === 2
                                                                            ? currentFinancialYear?.startYear +
                                                                              "-" +
                                                                              currentFinancialYear?.endYear
                                                                            : "",
                                                                },
                                                            })
                                                        }
                                                    />
                                                </Col>
                                                <Col sm={8}>
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input"
                                                            type="text"
                                                            placeholder=""
                                                            value={
                                                                configurationSale?.document?.suffix
                                                            }
                                                            onChange={e =>
                                                                setConfigurationSale({
                                                                    ...configurationSale,
                                                                    document: {
                                                                        ...configurationSale?.document,
                                                                        suffix: e.target.value,
                                                                    },
                                                                })
                                                            }
                                                        />
                                                        <Form.Label>Suffix</Form.Label>
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : null}
                                    {configurationSale.document?.prefix ||
                                    configurationSale.document?.start_with_number ||
                                    configurationSale.document?.suffix ? (
                                        <p className="mb-0 text-gray-700 fs-7">
                                            This Is How Your Invoice Number Will Look Like
                                        </p>
                                    ) : null}
                                    {/* <p className="mb-0 h5 fw-medium">
                                Example: INV-01-2023/24
                            </p> */}
                                    <p className="h5 fw-medium mb-3">
                                        {configurationSale.document?.prefix}
                                        {configurationSale.document?.start_with_number === ""
                                            ? 0
                                            : configurationSale.document?.start_with_number}
                                        {configurationSale.document?.suffix}
                                    </p>
                                    {/* {configurationSale?.document?.bank_id && (
                                        <div className="mt-5 sales-table_discount">
                                            <ReactSelect
                                                options={bankOptions}
                                                placeholder={"Default Bank"}
                                                defaultLabel="Select Bank"
                                                value={configurationSale?.document?.bank_id || null}
                                                onChange={e =>
                                                    setConfigurationSale({
                                                        ...configurationSale,
                                                        document: {
                                                            ...configurationSale?.document,
                                                            bank_id: e.value,
                                                        },
                                                    })
                                                }
                                            />
                                        </div>
                                    )} */}
                                  {isBankDetails && <>
                                    <div className="input-group flex-nowrap mb-2">
                                        <div
                                            className={`position-relative h-40px w-100 focus-shadow pe-36px`}
                                        >
                                            <ReactSelect
                                                options={bankOptions || []}
                                                placeholder={"Default Bank"}
                                                defaultLabel={"Select Bank"}
                                                value={configurationSale?.document?.bank_id || null}
                                                onChange={e =>
                                                    setConfigurationSale({
                                                        ...configurationSale,
                                                        document: {
                                                            ...configurationSale?.document,
                                                            bank_id: e.value,
                                                            ...(!e.value && {apply_income_bank_details : 0})
                                                        },
                                                    })
                                                }
                                                className="h-40px"
                                                isEdit={true}
                                                handleOpen={() =>
                                                    openPaymentLedgerModel(
                                                        configurationSale?.document?.bank_id
                                                    )
                                                }
                                            />
                                        </div>
                                        <button
                                            type="button"
                                            className="input-group-text custom-group-text"
                                            onClick={() => openPaymentLedgerModel("")}
                                        >
                                            <i className="fas fa-plus text-gray-900"></i>
                                        </button>
                                    </div>
                                    <div className="form-check form-check-custom w-100">
                                        <input
                                            className="form-check-input me-2"
                                            type="checkbox"
                                            name="apply_income_bank_details"
                                            id="isUseInAllIncomeTransactions"
                                            checked={configurationSale?.document?.apply_income_bank_details == 1 ? true : false}
                                            onChange={e => handleUpdateBankOnPastInvoices(e)}
                                        />
                                        <label
                                            htmlFor="isUseInAllIncomeTransactions"
                                            className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                        >
                                            Update bank details on past invoices too?
                                        </label>
                                    </div>
                                  </>
                                }
                                    {configurationSale?.document?.title_of_print !== undefined && (
                                        <div className="mt-5 sales-table_discount">
                                            {!isPurchaseOrderTransaction ? (
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input"
                                                        type="text"
                                                        placeholder=""
                                                        value={
                                                            configurationSale?.document
                                                                ?.title_of_print
                                                        }
                                                        onChange={e =>
                                                            setConfigurationSale({
                                                                ...configurationSale,
                                                                document: {
                                                                    ...configurationSale?.document,
                                                                    title_of_print: e.target.value,
                                                                },
                                                            })
                                                        }
                                                    />
                                                    <Form.Label>Default Title of Print</Form.Label>
                                                </Form.Group>
                                            ) : (
                                                <Form.Group>
                                                    <div className="input-group flex-nowrap">
                                                        <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                            <ReactSelect
                                                                options={purchaseOrderOptions}
                                                                placeholder={
                                                                    "Default Title of Print"
                                                                }
                                                                defaultLabel={"Select Title"}
                                                                islabel={true}
                                                                value={
                                                                    configurationSale?.document
                                                                        ?.title_of_print || null
                                                                }
                                                                onChange={e =>
                                                                    setConfigurationSale({
                                                                        ...configurationSale,
                                                                        document: {
                                                                            ...configurationSale?.document,
                                                                            title_of_print: e.value,
                                                                        },
                                                                    })
                                                                }
                                                                portal={true}
                                                                radius={true}
                                                                width="400px"
                                                                className="h-40px"
                                                                isEdit={false}
                                                            />
                                                        </div>
                                                        <button
                                                            type="button"
                                                            className="input-group-text custom-group-text"
                                                            onClick={() =>
                                                                setIsDefaultTitleModal(true)
                                                            }
                                                        >
                                                            <i className="fas fa-plus text-gray-900"></i>
                                                        </button>
                                                    </div>
                                                </Form.Group>
                                            )}
                                        </div>
                                    )}
                                    {configurationSale?.document?.default_title !== undefined && (
                                        <div className="mt-5 sales-table_discount">
                                            <Form.Group>
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                        <ReactSelect
                                                            options={
                                                                isEstimateTransaction
                                                                    ? estimateQuoteTitleOptions
                                                                    : []
                                                            }
                                                            placeholder={"Default Title"}
                                                            defaultLabel={"Select Title"}
                                                            islabel={true}
                                                            value={
                                                                configurationSale?.document
                                                                    ?.default_title || null
                                                            }
                                                            onChange={handleChangeDefaultTitle}
                                                            portal={true}
                                                            radius={true}
                                                            width="400px"
                                                            className="h-40px"
                                                            isEdit={false}
                                                        />
                                                    </div>
                                                    <button
                                                        type="button"
                                                        className="input-group-text custom-group-text"
                                                        onClick={() => setIsDefaultTitleModal(true)}
                                                    >
                                                        <i className="fas fa-plus text-gray-900"></i>
                                                    </button>
                                                </div>
                                            </Form.Group>
                                        </div>
                                    )}
                                </div>
                            </>
                        )}
                        {/* ) : (
                            ""
                        )} */}
                        <h4>Header</h4>
                        <div className="desc-box mb-5">
                            {HeaderList.map((item, index) => (
                                <div
                                    key={index}
                                    className="d-flex align-items-center justify-content-between"
                                >
                                    <p className="mb-0 h5 fw-medium">{item?.name}</p>
                                    <div
                                        className="form-check form-switch d-flex gap-2 my-3"
                                        style={{
                                            paddingLeft: "0",
                                        }}
                                    >
                                        <input
                                            className="form-check-input"
                                            style={{
                                                float: "right",
                                                marginLeft: "0",
                                            }}
                                            type="checkbox"
                                            checked={item.value}
                                            onChange={e => onToggle(e.target.checked, item.key)}
                                        />
                                    </div>
                                </div>
                            ))}
                            <div className="d-flex align-items-center justify-content-between mt-3">
                                <p className="mb-0 h5 fw-medium text-primary">Custom field</p>
                                <button
                                    className="btn btn-primary btn-sm"
                                    type="button"
                                    onClick={() => openCustomField(false)}
                                    style={{ padding: "6px 14px 6px 10px" }}
                                >
                                    ADD <span className="font-semibold fs-4">+</span>
                                </button>
                                <AddCustomField
                                    transactionType={getTransactionType}
                                    configurationUrl={configurationUrlForUpdateField}
                                    getCustomItemTransactionType={getCustomItemTransactionType}
                                    is_rearrange={true}
                                    isEditModel={isEditModel}
                                    setIsEditModel={setIsEditModel}
                                    invoice_type={itemType == "item" ? 1 : 2}
                                />
                            </div>
                            {customHeaderList?.map((custom, index) => (
                                <div
                                    key={index}
                                    className="d-flex align-items-center justify-content-between"
                                >
                                    <p className="mb-0 h5 fw-medium">{custom?.label_name}</p>
                                    <div className="d-flex justify-content-end align-items-center">
                                        <button
                                            type="button"
                                            className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                            onClick={e => handleOpenEditModel(custom)}
                                        >
                                            <i className="fas fs-4 fa-edit text-primary mx-auto my-3"></i>
                                        </button>
                                        <button
                                            type="button"
                                            className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                            onClick={() => handleOpenDeleteModal(custom)}
                                        >
                                            <i className="fas fs-4 fa-trash-alt text-danger mx-auto my-3"></i>
                                        </button>
                                        <div
                                            className="form-check ms-4 form-switch d-flex gap-2 my-3"
                                            style={{
                                                paddingLeft: "0",
                                            }}
                                        >
                                            <input
                                                className="form-check-input"
                                                style={{
                                                    float: "right",
                                                    marginLeft: "0",
                                                }}
                                                type="checkbox"
                                                checked={custom.is_enabled}
                                                onChange={e =>
                                                    onUpdateCustomField(e.target.checked, custom)
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <h4>Item Table Configuration</h4>
                        <div className="desc-box mb-5">
                            {TableList.map((item, index) => (
                                <div
                                    className="d-flex align-items-center justify-content-between"
                                    key={index}
                                >
                                    <p className="mb-0 h5 fw-medium">{item.name}</p>
                                    <div
                                        className="form-check form-switch d-flex gap-2 my-3"
                                        style={{
                                            paddingLeft: "0",
                                        }}
                                    >
                                        <input
                                            className="form-check-input"
                                            style={{
                                                float: "right",
                                                marginLeft: "0",
                                            }}
                                            type="checkbox"
                                            id="isTcsApplicable"
                                            checked={item.value}
                                            onChange={e => onToggle(e.target.checked, item.key)}
                                        />
                                    </div>
                                </div>
                            ))}
                            <div className="d-flex align-items-center justify-content-between mt-3">
                                {itemType == "item" ? <p className="mb-0 h5 fw-medium text-primary">Custom field</p> : ""}
                                <button
                                    className="btn btn-primary btn-sm"
                                    type="button"
                                    onClick={openCustomFieldItemModal}
                                    style={{ padding: "6px 14px 6px 10px" }}
                                >
                                    ADD <span className="font-semibold fs-4">+</span>
                                </button>
                                <AddItemMasterCustomFieldModal
                                    show={openCfItemModal}
                                    handleClose={handleCloseCfModal}
                                    transaction_type={getCustomItemTransactionType}
                                />
                            </div>
                            {itemTableCustomFieldList?.map((custom, index) => (
                                <div
                                    key={index}
                                    className="d-flex align-items-center justify-content-between"
                                >
                                    <p className="mb-0 h5 fw-medium">
                                        {custom.header == "HSN-SAC" ? "HSN/SAC" : custom.header}
                                    </p>
                                    <div className="d-flex justify-content-end align-items-center">
                                        {/* {custom?.eligible_for_formula ||
                                        custom?.name == "Quantity" ? (
                                            <button
                                                type="button"
                                                className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                                onClick={e => handleCustomFormula(custom)}
                                            >
                                                <i class="fa-solid fs-4 fa-calculator mx-auto my-3 text-black"></i>
                                            </button>
                                        ) : (
                                            ""
                                        )} */}
                                        {custom?.name !== "Quantity" ? (
                                            <>
                                                <button
                                                    type="button"
                                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                                    onClick={e => openCustomFieldItemModal(custom)}
                                                >
                                                    <i className="fas fs-4 fa-edit text-primary mx-auto my-3"></i>
                                                </button>
                                                {/* <button
                                                    type="button"
                                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                                    onClick={e => handleOpenItemEditModel(custom)}
                                                >
                                                    <i className="fas fs-4 fa-edit text-primary mx-auto my-3"></i>
                                                </button>
                                                <button
                                                    type="button"
                                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                                    onClick={() =>
                                                        handleOpenDeleteItemModal(custom)
                                                    }
                                                >
                                                    <i className="fas fs-4 fa-trash-alt text-danger mx-auto my-3"></i>
                                                </button> */}
                                                <div
                                                    className="form-check ms-4 form-switch d-flex gap-2 my-3"
                                                    style={{
                                                        paddingLeft: "0",
                                                    }}
                                                >
                                                    <input
                                                        className="form-check-input"
                                                        style={{
                                                            float: "right",
                                                            marginLeft: "0",
                                                        }}
                                                        type="checkbox"
                                                        checked={custom.is_enabled}
                                                        onChange={e =>
                                                            handleChangeItemMasterToggle(custom)
                                                        }
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            ""
                                        )}
                                    </div>
                                </div>
                            ))}
                            <div className="d-flex align-items-center justify-content-between mt-3">
                                <p className="mb-0 h5 fw-medium">Rearrange Item Table Headers</p>
                                <RearrangeItemModal />
                            </div>
                        </div>

                        <h4>Footer</h4>
                        <div className="desc-box mb-5">
                            {footerList.map((item, index) => (
                                <div
                                    className="d-flex align-items-center justify-content-between"
                                    key={index}
                                >
                                    {item.name === "Terms & Conditions" ? (
                                        <div className="d-flex align-content-center gap-4">
                                            <p className="mb-0 h5 fw-medium w-100">{item.name}</p>
                                            <span
                                                className="cursor_pointer"
                                                onClick={() => setIsTermsConditions(true)}
                                            >
                                                {" "}
                                                <i className="fas fa-pencil text-primary"></i>
                                            </span>
                                        </div>
                                    ) : (
                                        <p className="mb-0 h5 fw-medium w-100">{item.name}</p>
                                    )}

                                    {item.name === "Round off" ? (
                                        <div className="input-group justify-content-end">
                                            <Row className="min-w-180px">
                                                <Col sm={6} className="w-100 sales-table_discount">
                                                    <ReactSelect
                                                        value={item.value || null}
                                                        options={roundOffOption}
                                                        placeholder={"Round off"}
                                                        onChange={e => onToggle(e.value, item.key)}
                                                        position="top"
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <div
                                            className="form-check form-switch d-flex gap-2 my-3"
                                            style={{
                                                paddingLeft: "0",
                                            }}
                                        >
                                            <input
                                                className="form-check-input"
                                                style={{
                                                    float: "right",
                                                    marginLeft: "0",
                                                }}
                                                type="checkbox"
                                                id="isTcsApplicable"
                                                checked={item.value}
                                                onChange={e => onToggle(e.target.checked, item.key)}
                                            />
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="offcanvas-footer">
                        <div className="d-flex gap-4 fixed-buttons rounded-0">
                            <button
                                onClick={handleSubmit}
                                type="submit"
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                            <button
                                type="button"
                                onClick={closeSaleModel}
                                className="btn btn-secondary"
                            >
                                Back
                            </button>
                        </div>
                    </div>
                </form>
            </Modal>
            <TermsAndConditionsModal
                isTermsConditions={isTermsConditions}
                handleCloseModal={() => setIsTermsConditions(false)}
                configurationTerms={
                    configurationSale?.document?.terms_and_conditions?.length > 0
                        ? configurationSale?.document?.terms_and_conditions
                        : configurationSale?.footer?.terms_and_conditions
                }
                onToggle={onToggle}
            />
            <DefaultTitleModal
                isDefaultTitleModal={isDefaultTitleModal}
                handleCloseModal={() => setIsDefaultTitleModal(false)}
                isEstimateTransaction={isEstimateTransaction}
            />
            {isDeleteModal && (
                <WarningModal
                    show={isDeleteModal}
                    title="Delete!"
                    message="Are you sure want to delete this custom field?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={() => setIsDeleteModal(false)}
                    handleSubmit={handleDeleteCustomHeaderField}
                />
            )}
            {isDeleteCustomItem && (
                <WarningModal
                    show={isDeleteCustomItem}
                    title="Delete!"
                    message="Are you sure want to delete this custom field?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={() => setIsDeleteCustomItem(false)}
                    handleSubmit={handleConfirmDelete}
                />
            )}
            {isCustomFormula && (
                <CustomFormulaModal
                    show={isCustomFormula}
                    close={closeCustomFormulaModel}
                    itemTableCustomFieldList={itemTableCustomFieldList}
                    customFieldFormula={customFieldFormula}
                    invoice_type={itemType == "item" ? 1 : 2}
                    getCustomItemTransactionType={getCustomItemTransactionType}
                />
            )}
            {openLedgerModel && (
                <AddLedgerModal
                    show={openLedgerModel}
                    handleClose={closeBankLedgerModel}
                    id={1}
                    sale={false}
                    name={ledgerModelName}
                    setLedgerModelName={setLedgerModelName}
                    action={currentAction}
                    gstQuote={gstQuote}
                    itemType={itemType}
                    ledgerDetailOptions={ledgerDetailOptions}
                    configurationSale={configurationSale}
                    setConfigurationSale={setConfigurationSale}
                />
            )}
        </>
    );
};

export default ConfigurationModal;
