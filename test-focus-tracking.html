<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select, textarea {
            width: 200px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .primary-btn {
            background-color: #007bff;
            color: white;
        }
        .secondary-btn {
            background-color: #6c757d;
            color: white;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .close {
            font-size: 24px;
            cursor: pointer;
        }
        .focus-indicator {
            background-color: #e7f3ff;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <h1>Focus Tracking Test</h1>
    <p>This test simulates the focus tracking functionality for modal operations.</p>
    
    <form>
        <div class="form-group">
            <label for="field1">Field 1:</label>
            <input type="text" id="field1" name="field1" placeholder="Enter text">
        </div>
        
        <div class="form-group">
            <label for="field2">Field 2:</label>
            <input type="text" id="field2" name="field2" placeholder="Enter text">
        </div>
        
        <div class="form-group">
            <label for="party-select">Party:</label>
            <div style="display: flex; align-items: center;">
                <select id="party-select" name="party">
                    <option value="">Select Party</option>
                    <option value="party1">Party 1</option>
                    <option value="party2">Party 2</option>
                </select>
                <button type="button" id="add-party-btn" class="primary-btn" style="margin-left: 10px;">+</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="field3">Field 3:</label>
            <input type="text" id="field3" name="field3" placeholder="Enter text">
        </div>
        
        <div class="form-group">
            <label for="field4">Field 4:</label>
            <textarea id="field4" name="field4" placeholder="Enter description"></textarea>
        </div>
        
        <div class="form-group">
            <label for="field5">Field 5:</label>
            <input type="number" id="field5" name="field5" placeholder="Enter number">
        </div>
    </form>

    <!-- Modal -->
    <div id="ledger-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Ledger</h3>
                <span class="close" id="close-modal">&times;</span>
            </div>
            <form id="modal-form">
                <div class="form-group">
                    <label for="ledger-name">Ledger Name:</label>
                    <input type="text" id="ledger-name" name="ledger-name" placeholder="Enter ledger name" required>
                </div>
                
                <div class="form-group">
                    <label for="ledger-type">Ledger Type:</label>
                    <select id="ledger-type" name="ledger-type">
                        <option value="customer">Customer</option>
                        <option value="supplier">Supplier</option>
                        <option value="bank">Bank</option>
                    </select>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" id="cancel-btn" class="secondary-btn">Cancel</button>
                    <button type="submit" id="save-btn" class="primary-btn">Save</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Focus tracking utility (simplified version of the React implementation)
        class FocusTracker {
            constructor() {
                this.nextFocusElement = null;
            }

            trackNextFocusElement(currentElement) {
                // Get all focusable elements
                const focusableElements = document.querySelectorAll(
                    'input:not([disabled]):not([type="hidden"]):not([type="submit"]):not([type="button"]), textarea:not([disabled]), select:not([disabled]), button:not([disabled])'
                );

                // Filter out elements that are not visible or not focusable
                const focusableArray = Array.from(focusableElements).filter(element => {
                    const style = window.getComputedStyle(element);
                    const rect = element.getBoundingClientRect();

                    return (
                        style.display !== "none" &&
                        style.visibility !== "hidden" &&
                        element.offsetParent !== null &&
                        !element.hasAttribute("readonly") &&
                        rect.width > 0 &&
                        rect.height > 0 &&
                        !element.closest('.modal') // Exclude modal elements
                    );
                });

                const currentIndex = focusableArray.indexOf(currentElement);
                
                if (currentIndex !== -1 && currentIndex < focusableArray.length - 1) {
                    // Store the next focusable element
                    this.nextFocusElement = focusableArray[currentIndex + 1];
                    console.log('Tracked next focus element:', this.nextFocusElement);
                } else {
                    // If no next element, clear the reference
                    this.nextFocusElement = null;
                    console.log('No next element to track');
                }
            }

            focusTrackedElement() {
                if (this.nextFocusElement && document.contains(this.nextFocusElement)) {
                    try {
                        // Small delay to ensure modal is fully closed
                        setTimeout(() => {
                            this.nextFocusElement.focus();
                            this.nextFocusElement.classList.add('focus-indicator');
                            setTimeout(() => {
                                this.nextFocusElement.classList.remove('focus-indicator');
                            }, 1000);
                            console.log('Focused on tracked element:', this.nextFocusElement);
                            this.nextFocusElement = null; // Clear after focusing
                        }, 100);
                    } catch (error) {
                        console.warn('Failed to focus tracked element:', error);
                        this.nextFocusElement = null;
                    }
                } else {
                    console.log('No tracked element to focus on');
                }
            }

            clearTrackedElement() {
                this.nextFocusElement = null;
                console.log('Cleared tracked element');
            }
        }

        // Initialize focus tracker
        const focusTracker = new FocusTracker();

        // Modal elements
        const modal = document.getElementById('ledger-modal');
        const addPartyBtn = document.getElementById('add-party-btn');
        const closeModal = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-btn');
        const modalForm = document.getElementById('modal-form');

        // Open modal function
        function openModal() {
            // Track the next focusable element before opening the modal
            const currentElement = document.activeElement;
            if (currentElement) {
                focusTracker.trackNextFocusElement(currentElement);
            }
            
            modal.style.display = 'block';
            // Focus on first input in modal
            setTimeout(() => {
                document.getElementById('ledger-name').focus();
            }, 100);
        }

        // Close modal function
        function closeModal() {
            modal.style.display = 'none';
            // Focus on the tracked element after modal closes
            focusTracker.focusTrackedElement();
        }

        // Event listeners
        addPartyBtn.addEventListener('click', openModal);
        closeModal.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // Handle modal form submission
        modalForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            closeModal(); // This will trigger focus tracking
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Add Enter key navigation (simplified version)
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey && !e.altKey) {
                // Don't interfere with form submission
                if (e.target.tagName === 'BUTTON' || e.target.type === 'submit') {
                    return;
                }
                
                // Get all focusable elements
                const focusableElements = document.querySelectorAll(
                    'input:not([disabled]):not([type="hidden"]):not([type="submit"]):not([type="button"]), textarea:not([disabled]), select:not([disabled]), button:not([disabled])'
                );

                const focusableArray = Array.from(focusableElements).filter(element => {
                    const style = window.getComputedStyle(element);
                    const rect = element.getBoundingClientRect();

                    return (
                        style.display !== "none" &&
                        style.visibility !== "hidden" &&
                        element.offsetParent !== null &&
                        !element.hasAttribute("readonly") &&
                        rect.width > 0 &&
                        rect.height > 0
                    );
                });

                const currentElement = e.target;
                const currentIndex = focusableArray.indexOf(currentElement);

                if (currentIndex !== -1 && currentIndex < focusableArray.length - 1) {
                    e.preventDefault();
                    // Move to next focusable element
                    const nextElement = focusableArray[currentIndex + 1];
                    nextElement.focus();
                }
            }
        });

        console.log('Focus tracking test initialized');
    </script>
</body>
</html>
